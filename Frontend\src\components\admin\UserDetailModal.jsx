import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentUserDetail,
  selectLoading,
  hideUserDetailModal,
  setUserDetailLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  updateUser,
  deleteUser,
  fetchUserById,
} from "../../redux/slices/adminDashboardThunks";
import { showSuccess, showError } from "../../utils/toast";
import "../../styles/UserDetailModal.css";

// Icons
import {
  FaTimes,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaCalendarAlt,
  FaDollarSign,
  FaShoppingCart,
  FaEdit,
  FaTrash,
  FaCheckCircle,
  FaTimesCircle,
  FaSave,
  FaSpinner,
  FaExclamationTriangle,
} from "react-icons/fa";
import { MdVerified, MdBlock } from "react-icons/md";
import { IMAGE_BASE_URL } from "../../utils/constants";

const UserDetailModal = () => {
  const dispatch = useDispatch();
  const currentUser = useSelector(selectCurrentUserDetail);
  const loading = useSelector(selectLoading);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Helper function to convert numeric status to string
  const getStatusString = (status) => {
    switch (status) {
      case 1:
        return "active";
      case 0:
        return "inactive";
      case -1:
        return "deleted";
      default:
        return "active";
    }
  };

  // Helper function to convert string status to numeric
  const getStatusNumber = (status) => {
    switch (status) {
      case "active":
        return 1;
      case "inactive":
        return 0;
      case "deleted":
        return -1;
      default:
        return 1;
    }
  };

  // Initialize form when user data changes
  useEffect(() => {
    if (currentUser) {
      setEditForm({
        firstName: currentUser?.firstName || "",
        lastName: currentUser?.lastName || "",
        email: currentUser?.email || "",
        phone: currentUser?.mobile || currentUser?.phone || "",
        role: currentUser?.role || "buyer",
        status: getStatusString(currentUser?.status),
      });
    }
  }, [currentUser]);

  if (!currentUser) {
    return null;
  }

  // Form validation
  const validateForm = () => {
    const errors = {};

    if (!editForm.firstName?.trim()) {
      errors.firstName = "First name is required";
    }

    if (!editForm.lastName?.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!editForm.email?.trim()) {
      errors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editForm.email)) {
      errors.email = "Please enter a valid email address";
    }

    if (!editForm.phone?.trim()) {
      errors.phone = "Phone number is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleClose = () => {
    dispatch(hideUserDetailModal());
    setIsEditing(false);
    setFormErrors({});
    setShowDeleteConfirm(false);
    setIsSubmitting(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setFormErrors({});
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      dispatch(setUserDetailLoading(true));

      // Prepare data for backend (convert status to numeric and phone to mobile)
      const updateData = {
        ...editForm,
        status: getStatusNumber(editForm.status),
        mobile: editForm.phone, // Backend expects 'mobile' field
      };
      delete updateData.phone; // Remove phone field since we're using mobile

      // Dispatch the async thunk
      await dispatch(
        updateUser({
          id: currentUser.id || currentUser._id,
          userData: updateData,
        })
      ).unwrap();

      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_update",
          description: `User updated: ${editForm.firstName} ${editForm.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      setIsEditing(false);
      setFormErrors({});

      // Refresh user data to show updated information
      if (currentUser.id || currentUser._id) {
        dispatch(fetchUserById(currentUser.id || currentUser._id));
      }

      // Show success notification
      showSuccess(
        `User "${editForm.firstName} ${editForm.lastName}" has been updated successfully!`
      );
    } catch (error) {
      console.error("Failed to update user:", error);
      setFormErrors({
        general: error.message || "Failed to update user. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
      dispatch(setUserDetailLoading(false));
    }
  };

  const handleToggleStatus = async () => {
    const currentStatusNum = currentUser.status;
    const newStatusNum = currentStatusNum === 1 ? 0 : 1; // Toggle between active (1) and inactive (0)
    const actionText = newStatusNum === 1 ? "activate" : "deactivate";

    if (!window.confirm(`Are you sure you want to ${actionText} this user?`)) {
      return;
    }

    try {
      setIsSubmitting(true);
      dispatch(setUserDetailLoading(true));

      // Dispatch the async thunk
      await dispatch(
        updateUser({
          id: currentUser.id || currentUser._id,
          userData: { status: newStatusNum },
        })
      ).unwrap();

      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_status_change",
          description: `User ${getStatusString(newStatusNum)}: ${
            currentUser.firstName
          } ${currentUser.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      // Refresh user data
      if (currentUser.id || currentUser._id) {
        dispatch(fetchUserById(currentUser.id || currentUser._id));
      }

      showSuccess(`User has been ${actionText}d successfully!`);
    } catch (error) {
      console.error("Failed to update user status:", error);
      setFormErrors({
        general:
          error.message || "Failed to update user status. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
      dispatch(setUserDetailLoading(false));
    }
  };

  const handleDelete = async () => {
    try {
      setIsSubmitting(true);
      dispatch(setUserDetailLoading(true));

      // Dispatch the async thunk
      await dispatch(deleteUser(currentUser.id || currentUser._id)).unwrap();

      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_deletion",
          description: `User deleted: ${currentUser.firstName} ${currentUser.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      handleClose();
      showSuccess(
        `User "${currentUser.firstName} ${currentUser.lastName}" has been deleted successfully!`
      );
    } catch (error) {
      console.error("Failed to delete user:", error);
      setFormErrors({
        general: error.message || "Failed to delete user. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
      dispatch(setUserDetailLoading(false));
    }
  };

  const confirmDelete = () => {
    setShowDeleteConfirm(true);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 1:
        return <FaCheckCircle className="status-icon active" title="Active" />;
      case 0:
        return (
          <FaTimesCircle className="status-icon inactive" title="Inactive" />
        );
      case -1:
        return <FaTrash className="status-icon deleted" title="Deleted" />;
      default:
        return (
          <FaTimesCircle className="status-icon inactive" title="Unknown" />
        );
    }
  };

  const getRoleBadge = (role) => {
    const badges = {
      buyer: { color: "#3b82f6", label: "Buyer" },
      seller: { color: "#10b981", label: "Seller" },
      admin: { color: "#f59e0b", label: "Admin" },
    };
    const badge = badges[role] || badges.buyer;

    return (
      <span
        className="role-badge"
        style={{ backgroundColor: `${badge.color}20`, color: badge.color }}
      >
        {badge.label}
      </span>
    );
  };

  return (
    <div className="UserDetailModal">
      <div className="UserDetailModal__overlay" onClick={handleClose} />
      <div className="UserDetailModal__container">
        {/* Header */}
        <div className="UserDetailModal__header">
          <div className="header-info">
            <div className="user-avatar">
              {currentUser?.profileImage ? (
                <img
                  src={IMAGE_BASE_URL + currentUser.profileImage}
                  alt={currentUser?.firstName || "User"}
                />
              ) : (
                <FaUser />
              )}
            </div>
            <div className="user-basic-info">
              <h2>
                {currentUser?.firstName || "Unknown"}{" "}
                {currentUser?.lastName || "User"}
              </h2>
              <div className="user-badges">
                {getRoleBadge(currentUser?.role)}
                {getStatusIcon(currentUser?.status)}
                {currentUser?.verificationStatus === "verified" && (
                  <MdVerified
                    className="verification-icon verified"
                    title="Verified User"
                  />
                )}
              </div>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="UserDetailModal__content">
          {/* User Information */}
          <div className="info-section">
            <div className="section-header">
              <h3>User Information</h3>
              {!isEditing && (
                <button className="btn btn-outline" onClick={handleEdit}>
                  <FaEdit />
                  Edit User
                </button>
              )}
            </div>

            {isEditing ? (
              <div className="edit-form">
                {formErrors.general && (
                  <div className="error-message general-error">
                    <FaExclamationTriangle />
                    {formErrors.general}
                  </div>
                )}

                <div className="form-row">
                  <div className="form-group">
                    <label>First Name *</label>
                    <input
                      type="text"
                      value={editForm.firstName}
                      onChange={(e) => {
                        setEditForm({ ...editForm, firstName: e.target.value });
                        if (formErrors.firstName) {
                          setFormErrors({ ...formErrors, firstName: "" });
                        }
                      }}
                      className={`form-input ${
                        formErrors.firstName ? "error" : ""
                      }`}
                      disabled={isSubmitting}
                    />
                    {formErrors.firstName && (
                      <span className="error-text">{formErrors.firstName}</span>
                    )}
                  </div>
                  <div className="form-group">
                    <label>Last Name *</label>
                    <input
                      type="text"
                      value={editForm.lastName}
                      onChange={(e) => {
                        setEditForm({ ...editForm, lastName: e.target.value });
                        if (formErrors.lastName) {
                          setFormErrors({ ...formErrors, lastName: "" });
                        }
                      }}
                      className={`form-input ${
                        formErrors.lastName ? "error" : ""
                      }`}
                      disabled={isSubmitting}
                    />
                    {formErrors.lastName && (
                      <span className="error-text">{formErrors.lastName}</span>
                    )}
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>Email *</label>
                    <input
                      type="email"
                      value={editForm.email}
                      onChange={(e) => {
                        setEditForm({ ...editForm, email: e.target.value });
                        if (formErrors.email) {
                          setFormErrors({ ...formErrors, email: "" });
                        }
                      }}
                      className={`form-input ${
                        formErrors.email ? "error" : ""
                      }`}
                      disabled={isSubmitting}
                    />
                    {formErrors.email && (
                      <span className="error-text">{formErrors.email}</span>
                    )}
                  </div>
                  <div className="form-group">
                    <label>Phone *</label>
                    <input
                      type="tel"
                      value={editForm.phone}
                      onChange={(e) => {
                        setEditForm({ ...editForm, phone: e.target.value });
                        if (formErrors.phone) {
                          setFormErrors({ ...formErrors, phone: "" });
                        }
                      }}
                      className={`form-input ${
                        formErrors.phone ? "error" : ""
                      }`}
                      disabled={isSubmitting}
                    />
                    {formErrors.phone && (
                      <span className="error-text">{formErrors.phone}</span>
                    )}
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>Role</label>
                    <select
                      value={editForm.role}
                      onChange={(e) =>
                        setEditForm({ ...editForm, role: e.target.value })
                      }
                      className="form-select"
                      disabled={isSubmitting}
                    >
                      <option value="buyer">Buyer</option>
                      <option value="seller">Seller</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label>Status</label>
                    <select
                      value={editForm.status}
                      onChange={(e) =>
                        setEditForm({ ...editForm, status: e.target.value })
                      }
                      className="form-select"
                      disabled={isSubmitting}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="deleted">Deleted</option>
                    </select>
                  </div>
                </div>
                <div className="form-actions">
                  <button
                    className="btn btn-primary"
                    onClick={handleSave}
                    disabled={isSubmitting || loading?.userDetail}
                  >
                    {isSubmitting ? (
                      <>
                        <FaSpinner className="spinner" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <FaSave />
                        Save Changes
                      </>
                    )}
                  </button>
                  <button
                    className="btn btn-outline"
                    onClick={() => {
                      setIsEditing(false);
                      setFormErrors({});
                    }}
                    disabled={isSubmitting || loading?.userDetail}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="info-grid">
                <div className="info-item">
                  <FaEnvelope className="info-icon" />
                  <div>
                    <span className="info-label">Email</span>
                    <span className="info-value">{currentUser.email}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaPhone className="info-icon" />
                  <div>
                    <span className="info-label">Phone</span>
                    <span className="info-value">{currentUser.phone}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Date Joined</span>
                    <span className="info-value">
                      {formatDate(currentUser.dateJoined)}
                    </span>
                  </div>
                </div>
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Last Login</span>
                    <span className="info-value">
                      {formatDate(currentUser.lastLogin)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Statistics */}
          {currentUser.role === "buyer" && (
            <div className="stats-section">
              <h3 className="pb-5">Purchase Statistics</h3>
              <div className="stats-grid">
                <div className="stat-card">
                  <FaShoppingCart className="stat-icon" />
                  <div className="stat-content">
                    <span className="stat-number">
                      {currentUser.totalPurchases}
                    </span>
                    <span className="stat-label">Total Purchases</span>
                  </div>
                </div>
                <div className="stat-card">
                  <FaDollarSign className="stat-icon" />
                  <div className="stat-content">
                    <span className="stat-number">
                      {formatCurrency(currentUser.totalSpent)}
                    </span>
                    <span className="stat-label">Total Spent</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Activity History */}
          <div className="activity-section">
            <h3>Recent Activity</h3>
            <div className="activity-list">
              {currentUser.activityHistory?.map((activity, index) => (
                <div key={index} className="activity-item">
                  <div className="activity-content">
                    <span className="activity-description">
                      {activity.action}
                    </span>
                    <span className="activity-date">
                      {formatDate(activity.date)}
                    </span>
                  </div>
                  {activity.amount && (
                    <span className="activity-amount">
                      {formatCurrency(activity.amount)}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="actions-section">
            <div className="action-buttons">
              <button
                className={`btn ${
                  currentUser?.status === 1 ? "btn-warning" : "btn-success"
                }`}
                onClick={handleToggleStatus}
                disabled={
                  isSubmitting ||
                  loading?.userDetail ||
                  currentUser?.status === -1
                }
              >
                {isSubmitting ? (
                  <FaSpinner className="spinner" />
                ) : currentUser?.status === 1 ? (
                  <>
                    <MdBlock />
                    Deactivate User
                  </>
                ) : (
                  <>
                    <FaCheckCircle />
                    Activate User
                  </>
                )}
              </button>
              <button
                className="btn btn-danger"
                onClick={confirmDelete}
                disabled={isSubmitting || loading?.userDetail}
              >
                <FaTrash />
                Delete User
              </button>
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          {showDeleteConfirm && (
            <div className="delete-confirm-overlay">
              <div className="delete-confirm-modal">
                <div className="delete-confirm-header">
                  <FaExclamationTriangle className="warning-icon" />
                  <h3>Confirm Deletion</h3>
                </div>
                <div className="delete-confirm-content">
                  <p>
                    Are you sure you want to delete user{" "}
                    <strong>
                      "{currentUser.firstName} {currentUser.lastName}"
                    </strong>
                    ?
                  </p>
                  <p className="warning-text">
                    This action cannot be undone. All user data will be
                    permanently removed.
                  </p>
                </div>
                <div className="delete-confirm-actions">
                  <button
                    className="btn btn-danger"
                    onClick={() => {
                      setShowDeleteConfirm(false);
                      handleDelete();
                    }}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <FaSpinner className="spinner" />
                        Deleting...
                      </>
                    ) : (
                      <>
                        <FaTrash />
                        Yes, Delete User
                      </>
                    )}
                  </button>
                  <button
                    className="btn btn-outline"
                    onClick={cancelDelete}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDetailModal;
