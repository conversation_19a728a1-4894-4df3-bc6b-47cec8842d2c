import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminReviewManagement.css";
import { FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaCheck, FaTimes, FaStar } from "react-icons/fa";

const AdminReviewManagement = () => {
    const dispatch = useDispatch();
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");
    const [ratingFilter, setRatingFilter] = useState("all");

    return (
        <AdminLayout>
            <div className="AdminReviewManagement">
                <div className="AdminReviewManagement__header">
                
                    <p>Manage and monitor all content reviews</p>
                </div>

                {/* Search and Filter Section */}
                <div className="AdminReviewManagement__controls">
                    <div className="search-box">
                        <FaSearch className="search-icon" />
                        <input
                            type="text"
                            placeholder="Search reviews..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>

                    <div className="filter-box">
                   
                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                        >
                            <option value="all">   All Status</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="flagged">Flagged</option>
                        </select>

                        <select
                            value={ratingFilter}
                            onChange={(e) => setRatingFilter(e.target.value)}
                        >
                            <option value="all">All Ratings</option>
                            <option value="5">5 Stars</option>
                            <option value="4">4 Stars</option>
                            <option value="3">3 Stars</option>
                            <option value="2">2 Stars</option>
                            <option value="1">1 Star</option>
                        </select>
                    </div>
                </div>

                {/* Table Section */}
                <div className="AdminReviewManagement__table">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" />
                                </th>
                                <th>Review ID</th>
                                <th>Content</th>
                                <th>Reviewer</th>
                                <th>Rating</th>
                                <th>Comment</th>
                                <th>Status</th>
                                <th>Created Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {/* Table rows will be populated here */}
                            <tr>
                                <td colSpan="9" className="no-data">
                                    Loading reviews...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                {/* Pagination Section */}
                <div className="AdminReviewManagement__pagination">
                    <button disabled>Previous</button>
                    <span>Page 1 of 1</span>
                    <button disabled>Next</button>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminReviewManagement; 